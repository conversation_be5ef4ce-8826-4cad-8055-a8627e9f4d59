#include <iostream>
#include <string>
#include <vector>
#include <chrono>
#include <thread>
#include <locale>
// 假设使用open62541库，这是一个流行的开源OPC UA库
// 如果您使用其他库，请相应调整头文件
#ifdef USE_OPEN62541
#include <winsock2.h>  // 必须在windows.h之前包含
#include <open62541/client.h>
#include <open62541/client_config_default.h>
#include <open62541/client_highlevel.h>
#include <open62541/plugin/log_stdout.h>
#endif

#ifdef _WIN32
#include <windows.h>
#include <io.h>
#include <fcntl.h>
#endif

class OPCUAClientConfig {
public:
    // 服务器连接配置
    std::string serverUrl = "opc.tcp://*************:4841/OPCUA/KepOPC";  // 服务器URL
    std::string serverName = "OPC UA Server";            // 服务器名称
    int serverPort = 4840;                               // 服务器端口
    std::string serverHost = "localhost";                // 服务器主机

    // 安全配置
    std::string securityPolicy = "None";                 // 安全策略: None, Basic128Rsa15, Basic256, Basic256Sha256
    std::string securityMode = "None";                   // 安全模式: None, Sign, SignAndEncrypt
    std::string certificatePath = "";                    // 客户端证书路径
    std::string privateKeyPath = "";                     // 客户端私钥路径
    std::string trustListPath = "";                      // 信任列表路径

    // 认证配置
    bool useAuthentication = false;                      // 是否使用认证
    std::string username = "";                           // 用户名
    std::string password = "";                           // 密码
    std::string authenticationMethod = "Anonymous";      // 认证方法: Anonymous, UserName, Certificate

    // 连接配置
    int connectionTimeout = 5000;                        // 连接超时时间(毫秒)
    int sessionTimeout = 60000;                          // 会话超时时间(毫秒)
    int requestTimeout = 10000;                          // 请求超时时间(毫秒)
    int maxRetryCount = 3;                               // 最大重试次数
    int retryDelay = 1000;                               // 重试延迟(毫秒)

    // 订阅配置
    double publishingInterval = 1000.0;                  // 发布间隔(毫秒)
    int maxNotificationsPerPublish = 10;                 // 每次发布的最大通知数
    int lifetimeCount = 10000;                           // 生命周期计数
    int maxKeepAliveCount = 3;                           // 最大保活计数
    int priority = 0;                                    // 优先级

    // 监控项配置
    double samplingInterval = 1000.0;                    // 采样间隔(毫秒)
    int queueSize = 10;                                  // 队列大小
    bool discardOldest = true;                           // 是否丢弃最旧的数据

    // 节点配置
    std::vector<std::string> nodeIds = {                 // 要监控的节点ID列表
        "Root\\Group1\\Group1.a2.PV",
        "Root\\Group1\\Group1.baojing1.PV",
        "Root\\Group1\\Group1.f11.PV",
        "Root\\Group1\\Group1.tag3.PV"
    };

    // 应用程序配置
    std::string applicationName = "OPC UA C++ Client";   // 应用程序名称

    // 日志配置
    bool enableLogging = false;                          // 是否启用日志（设为false减少输出）
    std::string logLevel = "ERROR";                      // 日志级别: ERROR, WARNING, INFO, DEBUG

    // 其他配置
    bool autoReconnect = true;                           // 是否自动重连
};

class OPCUAClient {
private:
    OPCUAClientConfig config;
    bool connected = false;

#ifdef USE_OPEN62541
    UA_Client* client = nullptr;
    UA_ClientConfig* clientConfig = nullptr;
#endif

public:
    OPCUAClient(const OPCUAClientConfig& cfg) : config(cfg) {
        initializeClient();
    }

    ~OPCUAClient() {
        disconnect();
        cleanup();
    }

    void initializeClient() {
#ifdef USE_OPEN62541
        client = UA_Client_new();
        clientConfig = UA_Client_getConfig(client);

        // 设置默认配置
        UA_ClientConfig_setDefault(clientConfig);

        // 应用配置参数
        clientConfig->timeout = config.connectionTimeout;

        // 设置应用程序描述
        clientConfig->clientDescription.applicationName =
            UA_LOCALIZEDTEXT((char*)"en-US", (char*)config.applicationName.c_str());

        // 禁用日志输出以避免乱码问题
        if (config.enableLogging) {
            // 设置日志级别
            UA_LogLevel logLevel = UA_LOGLEVEL_ERROR;  // 只显示错误信息
            if (config.logLevel == "TRACE") logLevel = UA_LOGLEVEL_TRACE;
            else if (config.logLevel == "DEBUG") logLevel = UA_LOGLEVEL_DEBUG;
            else if (config.logLevel == "WARNING") logLevel = UA_LOGLEVEL_WARNING;
            else if (config.logLevel == "ERROR") logLevel = UA_LOGLEVEL_ERROR;
            else if (config.logLevel == "FATAL") logLevel = UA_LOGLEVEL_FATAL;

            clientConfig->logger = UA_Log_Stdout_withLevel(logLevel);
        } else {
            // 设置最高日志级别来减少输出
            clientConfig->logger = UA_Log_Stdout_withLevel(UA_LOGLEVEL_FATAL);
        }
#endif

        std::cout << "OPC UA客户端初始化完成" << std::endl;
        printConfiguration();
    }

    bool connect() {
        std::cout << "正在连接到服务器: " << config.serverUrl << std::endl;

#ifdef USE_OPEN62541
        UA_StatusCode retval;

        if (config.useAuthentication && config.authenticationMethod == "UserName") {
            // 使用用户名密码认证
            retval = UA_Client_connectUsername(client, config.serverUrl.c_str(),
                                             config.username.c_str(), config.password.c_str());
        } else {
            // 匿名连接
            retval = UA_Client_connect(client, config.serverUrl.c_str());
        }

        if (retval == UA_STATUSCODE_GOOD) {
            connected = true;
            std::cout << "成功连接到服务器!" << std::endl;
            return true;
        } else {
            std::cout << "连接失败，状态码: " << UA_StatusCode_name(retval) << std::endl;
            return false;
        }
#else
        std::cout << "错误: 未编译OPC UA库支持!" << std::endl;
        std::cout << "请安装open62541库并使用 -DUSE_OPEN62541 编译选项" << std::endl;
        return false;
#endif
    }

    void disconnect() {
        if (connected) {
            std::cout << "正在断开连接..." << std::endl;

#ifdef USE_OPEN62541
            UA_Client_disconnect(client);
#endif
            connected = false;
            std::cout << "已断开连接" << std::endl;
        }
    }

    bool readNode(const std::string& nodeId) {
        if (!connected) {
            std::cout << "错误: 客户端未连接" << std::endl;
            return false;
        }

        std::cout << "读取节点: " << nodeId << std::endl;

#ifdef USE_OPEN62541
        // 简单地尝试不同的NodeId格式
        UA_NodeId node = UA_NODEID_STRING(2, (char*)nodeId.c_str());
        UA_Variant value;
        UA_Variant_init(&value);

        UA_StatusCode retval = UA_Client_readValueAttribute(client, node, &value);

        if (retval == UA_STATUSCODE_GOOD) {
            std::cout << "读取成功，数据类型: " << value.type->typeName << std::endl;

            // 根据数据类型解析并显示值
            if (UA_Variant_hasScalarType(&value, &UA_TYPES[UA_TYPES_DOUBLE])) {
                double val = *(UA_Double*)value.data;
                std::cout << "值: " << val << std::endl;
            } else if (UA_Variant_hasScalarType(&value, &UA_TYPES[UA_TYPES_FLOAT])) {
                float val = *(UA_Float*)value.data;
                std::cout << "值: " << val << std::endl;
            } else if (UA_Variant_hasScalarType(&value, &UA_TYPES[UA_TYPES_INT32])) {
                int32_t val = *(UA_Int32*)value.data;
                std::cout << "值: " << val << std::endl;
            } else if (UA_Variant_hasScalarType(&value, &UA_TYPES[UA_TYPES_BOOLEAN])) {
                bool val = *(UA_Boolean*)value.data;
                std::cout << "值: " << (val ? "true" : "false") << std::endl;
            } else if (UA_Variant_hasScalarType(&value, &UA_TYPES[UA_TYPES_STRING])) {
                UA_String* str = (UA_String*)value.data;
                std::string val(reinterpret_cast<char*>(str->data), str->length);
                std::cout << "值: " << val << std::endl;
            } else {
                std::cout << "值: [复杂数据类型]" << std::endl;
            }

            UA_Variant_clear(&value);
            return true;
        } else {
            std::cout << "读取失败，状态码: " << UA_StatusCode_name(retval) << std::endl;
            return false;
        }
#else
        std::cout << "错误: 未编译OPC UA库支持!" << std::endl;
        std::cout << "请安装open62541库并使用 -DUSE_OPEN62541 编译选项" << std::endl;
        return false;
#endif
    }



    void browseServerNodes() {
#ifdef USE_OPEN62541
        std::cout << "\n=== 浏览服务器节点结构 ===" << std::endl;

        // 浏览Objects文件夹
        UA_NodeId objectsNodeId = UA_NODEID_NUMERIC(0, UA_NS0ID_OBJECTSFOLDER);
        browseNode(objectsNodeId, "Objects", 0, 2);

        // 浏览Server对象
        UA_NodeId serverNodeId = UA_NODEID_NUMERIC(0, UA_NS0ID_SERVER);
        browseNode(serverNodeId, "Server", 0, 1);

        std::cout << "=== 节点浏览完成 ===\n" << std::endl;
#else
        std::cout << "错误: 未编译OPC UA库支持!" << std::endl;
#endif
    }

    void browseNode(UA_NodeId nodeId, const std::string& nodeName, int depth, int maxDepth) {
#ifdef USE_OPEN62541
        if (depth > maxDepth) return;

        // 打印当前节点
        for (int i = 0; i < depth; i++) std::cout << "  ";
        std::cout << nodeName << std::endl;

        UA_BrowseRequest browseRequest;
        UA_BrowseRequest_init(&browseRequest);
        browseRequest.requestedMaxReferencesPerNode = 0;
        browseRequest.nodesToBrowse = UA_BrowseDescription_new();
        browseRequest.nodesToBrowseSize = 1;
        browseRequest.nodesToBrowse[0].nodeId = nodeId;
        browseRequest.nodesToBrowse[0].resultMask = UA_BROWSERESULTMASK_ALL;

        UA_BrowseResponse browseResponse = UA_Client_Service_browse(client, browseRequest);

        if (browseResponse.responseHeader.serviceResult == UA_STATUSCODE_GOOD) {
            for (size_t i = 0; i < browseResponse.resultsSize; ++i) {
                for (size_t j = 0; j < browseResponse.results[i].referencesSize; ++j) {
                    UA_ReferenceDescription* ref = &browseResponse.results[i].references[j];

                    // 构建节点ID字符串
                    std::string nodeIdStr;
                    if (ref->nodeId.nodeId.identifierType == UA_NODEIDTYPE_NUMERIC) {
                        nodeIdStr = "ns=" + std::to_string(ref->nodeId.nodeId.namespaceIndex)
                                   + ";i=" + std::to_string(ref->nodeId.nodeId.identifier.numeric);
                    } else if (ref->nodeId.nodeId.identifierType == UA_NODEIDTYPE_STRING) {
                        nodeIdStr = "ns=" + std::to_string(ref->nodeId.nodeId.namespaceIndex)
                                   + ";s=" + std::string((char*)ref->nodeId.nodeId.identifier.string.data,
                                                        ref->nodeId.nodeId.identifier.string.length);
                    }

                    // 获取显示名称
                    std::string displayName = "Unknown";
                    if (ref->displayName.text.data) {
                        displayName = std::string((char*)ref->displayName.text.data, ref->displayName.text.length);
                    }

                    // 获取节点类型信息
                    std::string nodeTypeInfo = "";
                    UA_NodeClass nodeClass;
                    UA_StatusCode classResult = UA_Client_readNodeClassAttribute(client, ref->nodeId.nodeId, &nodeClass);
                    if (classResult == UA_STATUSCODE_GOOD) {
                        switch (nodeClass) {
                            case UA_NODECLASS_OBJECT:
                                nodeTypeInfo = " [Object]";
                                break;
                            case UA_NODECLASS_VARIABLE:
                                {
                                    nodeTypeInfo = " [Variable]";
                                    // 尝试读取变量值
                                    UA_Variant value;
                                    UA_Variant_init(&value);
                                    UA_StatusCode valueResult = UA_Client_readValueAttribute(client, ref->nodeId.nodeId, &value);
                                    if (valueResult == UA_STATUSCODE_GOOD) {
                                        if (UA_Variant_hasScalarType(&value, &UA_TYPES[UA_TYPES_DOUBLE])) {
                                            double val = *(UA_Double*)value.data;
                                            nodeTypeInfo += " = " + std::to_string(val);
                                        } else if (UA_Variant_hasScalarType(&value, &UA_TYPES[UA_TYPES_FLOAT])) {
                                            float val = *(UA_Float*)value.data;
                                            nodeTypeInfo += " = " + std::to_string(val);
                                        } else if (UA_Variant_hasScalarType(&value, &UA_TYPES[UA_TYPES_INT32])) {
                                            int32_t val = *(UA_Int32*)value.data;
                                            nodeTypeInfo += " = " + std::to_string(val);
                                        } else if (UA_Variant_hasScalarType(&value, &UA_TYPES[UA_TYPES_BOOLEAN])) {
                                            bool val = *(UA_Boolean*)value.data;
                                            nodeTypeInfo += " = " + std::string(val ? "true" : "false");
                                        }
                                        UA_Variant_clear(&value);
                                    }
                                }
                                break;
                            case UA_NODECLASS_METHOD:
                                nodeTypeInfo = " [Method]";
                                break;
                            case UA_NODECLASS_OBJECTTYPE:
                                nodeTypeInfo = " [ObjectType]";
                                break;
                            case UA_NODECLASS_VARIABLETYPE:
                                nodeTypeInfo = " [VariableType]";
                                break;
                            case UA_NODECLASS_REFERENCETYPE:
                                nodeTypeInfo = " [ReferenceType]";
                                break;
                            case UA_NODECLASS_DATATYPE:
                                nodeTypeInfo = " [DataType]";
                                break;
                            case UA_NODECLASS_VIEW:
                                nodeTypeInfo = " [View]";
                                break;
                            case UA_NODECLASS_UNSPECIFIED:
                            case __UA_NODECLASS_FORCE32BIT:
                            default:
                                nodeTypeInfo = " [Unknown]";
                                break;
                        }
                    }

                    // 打印节点信息
                    for (int k = 0; k < depth + 1; k++) std::cout << "  ";
                    std::cout << "├─ " << displayName << " [" << nodeIdStr << "]" << nodeTypeInfo << std::endl;

                    // 递归浏览子节点
                    if (depth < maxDepth) {
                        browseNode(ref->nodeId.nodeId, displayName, depth + 1, maxDepth);
                    }
                }
            }
        }

        UA_BrowseRequest_clear(&browseRequest);
        UA_BrowseResponse_clear(&browseResponse);
#endif
    }

    void readAllConfiguredNodes() {
        std::cout << "\n=== 读取所有配置的节点 ===" << std::endl;
        std::cout << "配置的节点数量: " << config.nodeIds.size() << std::endl;

        int successCount = 0;
        int failCount = 0;

        for (size_t i = 0; i < config.nodeIds.size(); i++) {
            const auto& nodeId = config.nodeIds[i];
            std::cout << "\n[" << (i + 1) << "/" << config.nodeIds.size() << "] ";

            if (readNode(nodeId)) {
                successCount++;
            } else {
                failCount++;
            }

            // 在节点之间添加短暂延迟
            std::this_thread::sleep_for(std::chrono::milliseconds(200));
        }

        std::cout << "\n=== 节点读取完成 ===" << std::endl;
        std::cout << "成功: " << successCount << " 个节点" << std::endl;
        std::cout << "失败: " << failCount << " 个节点" << std::endl;
        std::cout << "========================\n" << std::endl;
    }

    void printConfiguration() {
        std::cout << "\n=== OPC UA客户端配置 ===" << std::endl;
        std::cout << "服务器URL: " << config.serverUrl << std::endl;
        std::cout << "服务器名称: " << config.serverName << std::endl;
        std::cout << "安全策略: " << config.securityPolicy << std::endl;
        std::cout << "安全模式: " << config.securityMode << std::endl;
        std::cout << "认证方法: " << config.authenticationMethod << std::endl;
        std::cout << "连接超时: " << config.connectionTimeout << "ms" << std::endl;
        std::cout << "会话超时: " << config.sessionTimeout << "ms" << std::endl;
        std::cout << "应用程序名称: " << config.applicationName << std::endl;
        std::cout << "监控节点数量: " << config.nodeIds.size() << std::endl;

        if (!config.nodeIds.empty()) {
            std::cout << "配置的节点列表:" << std::endl;
            for (size_t i = 0; i < config.nodeIds.size(); i++) {
                std::cout << "  " << (i + 1) << ". " << config.nodeIds[i] << std::endl;
            }
        }

        std::cout << "自动重连: " << (config.autoReconnect ? "是" : "否") << std::endl;
        std::cout << "========================\n" << std::endl;
    }

    void cleanup() {
#ifdef USE_OPEN62541
        if (client) {
            UA_Client_delete(client);
            client = nullptr;
        }
#endif
    }

    bool isConnected() const {
        return connected;
    }

    // 获取和设置配置的方法
    OPCUAClientConfig& getConfig() {
        return config;
    }

    void setConfig(const OPCUAClientConfig& newConfig) {
        config = newConfig;
    }
};

// 基本使用示例
void basicUsageExample() {
    std::cout << "=== OPC UA 客户端基本使用 ===" << std::endl;

    // 创建默认配置
    OPCUAClientConfig config;

    // 在这里修改您的服务器配置
    // config.serverUrl = "opc.tcp://192.168.1.100:4840";
    // config.username = "admin";
    // config.password = "password";
    // config.useAuthentication = true;
    // config.authenticationMethod = "UserName";

    // 创建客户端
    OPCUAClient client(config);

    // 尝试连接到服务器
    if (client.connect()) {
        std::cout << "连接成功！" << std::endl;

        // 检查是否有配置的节点
        if (!config.nodeIds.empty()) {
            std::cout << "使用配置的节点列表..." << std::endl;
            // 读取所有配置的节点
            client.readAllConfiguredNodes();
        } else {
            std::cout << "配置的节点列表为空，开始浏览服务器..." << std::endl;
            // 浏览服务器节点结构
            client.browseServerNodes();
        }

        // 等待用户输入，保持程序运行
        std::cout << "\n=== 程序运行中 ===" << std::endl;
        std::cout << "按 Enter 键退出程序..." << std::endl;
        std::cin.get();  // 等待用户按回车键

        // 断开连接
        client.disconnect();
    } else {
        std::cout << "连接失败！请检查服务器配置和网络连接。" << std::endl;
    }
}

// 设置控制台编码支持中文
void setupConsoleEncoding() {
#ifdef _WIN32
    // 设置控制台代码页为UTF-8
    SetConsoleOutputCP(CP_UTF8);
    SetConsoleCP(CP_UTF8);

    // 设置控制台模式支持UTF-8
    HANDLE hOut = GetStdHandle(STD_OUTPUT_HANDLE);
    DWORD dwMode = 0;
    GetConsoleMode(hOut, &dwMode);
    dwMode |= ENABLE_PROCESSED_OUTPUT | ENABLE_WRAP_AT_EOL_OUTPUT | ENABLE_VIRTUAL_TERMINAL_PROCESSING;
    SetConsoleMode(hOut, dwMode);

    // 尝试设置中文locale，如果失败则使用默认
    try {
        std::locale::global(std::locale("zh_CN.UTF-8"));
    } catch (...) {
        try {
            std::locale::global(std::locale("Chinese"));
        } catch (...) {
            // 如果都失败，使用默认locale
            std::locale::global(std::locale("C"));
        }
    }
#endif
}

int main() {
    // 设置控制台编码
    setupConsoleEncoding();

    std::cout << "OPC UA C++ 客户端程序" << std::endl;
    std::cout << "=====================" << std::endl;

    try {
        // 运行基本使用示例
        basicUsageExample();

    } catch (const std::exception& e) {
        std::cerr << "发生异常: " << e.what() << std::endl;
        return 1;
    }

    std::cout << "\n程序执行完成。" << std::endl;
    std::cout << "\n配置说明：" << std::endl;
    std::cout << "1. 修改 OPCUAClientConfig 类中的默认值来配置您的服务器" << std::endl;
    std::cout << "2. 安装 open62541 库并使用 -DUSE_OPEN62541 编译选项启用OPC UA功能" << std::endl;
    std::cout << "3. 根据您的服务器调整 serverUrl、节点ID、安全设置和认证参数" << std::endl;

    return 0;
}